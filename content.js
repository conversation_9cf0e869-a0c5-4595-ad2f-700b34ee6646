// == content.js ==

// ==== بداية إضافة دوال WhatsApp إلى content.js ====

/*** دالة لتحويل رقم الجوال إلى تنسيق دولي ***/
window.normalizePhone = input => {
  const t = input.replace(/[\s\-–—]/g,'').trim();
  return t.startsWith('0') ? '966' + t.slice(1) : t;
};

/*** دالة لجلب تفاصيل طالب واحد من رابط ملف الطالب ***/
window.fetchStudentDetail = async function(url){
  try {
    const res  = await fetch(url, { credentials:'include' });
    const html = await res.text();
    const doc  = new DOMParser().parseFromString(html, 'text/html');

    const name  = doc.querySelector("h2#FullNameId")?.innerText.trim() || "";
    const raw   = doc.querySelector("#ParentMobile")?.value.trim()  || "";
    const phone = window.normalizePhone(raw);
    const h6    = doc.querySelector("h6.fw-medium");
    let clsRaw  = "";
    if (h6) {
      const c = h6.cloneNode(true);
      c.querySelector("small")?.remove();
      clsRaw = c.innerText.trim();
    }
    clsRaw = clsRaw.replace(/[٠-٩]/g, d=>String.fromCharCode(d.charCodeAt(0)-0x0660+0x30));
    const parts = clsRaw.match(/^(.+?)[\s-]?(\d+)$/u);
    const classLabel = parts ? `${parts[1].trim()}-${parts[2]}` : clsRaw;

    if (name && phone && classLabel) {
      return { name, parentPhone: phone, classLabel };
    }
  } catch(e){
   // console.error("fetchStudentDetail failed:", e);
  }
  return null;
};

/*** دالة لجمع جميع الطلاب (ثم تخزينهم في chrome.storage.local كـ “studentsList”) ***/
window.collectStudents = async function(sid){
  if (!sid) return;
  const base = `https://schools.madrasati.sa/SchoolManagment/Students/ManageStudents/${sid}`;
  let page = 1, links = [];

  while(true){
    const html = await fetch(`${base}?PageNumber=${page}&years=-1&semesters=-1&classrooms=-1`, { credentials:'include' })
                      .then(r=>r.text());
    const doc  = new DOMParser().parseFromString(html,'text/html');
    const hrefs= Array.from(doc.querySelectorAll("a[href*='StudentInfo']")).map(a=>a.href);
    if (!hrefs.length) break;
    links.push(...hrefs);
    page++;
  }
  links = Array.from(new Set(links));
  const all = [];
  for (let url of links) {
    const stu = await window.fetchStudentDetail(url);
    if (stu) all.push(stu);
  }

  chrome.storage.local.set({ studentsList: all }, () => {
   // console.log(`✅ جمع ${all.length} طالب وخزنّاه في chrome.storage.local`);
  });
};

/***
 * دالة تُنفّذ عند الضغط على زر “💬 واتساب”:
 * 1) تجمع الطلاب إذا لزم الأمر
 * 2) تنتظر حتى يظهر chrome.storage.local.studentsList
 * 3) تخزن نسخة مبسطة في chrome.storage.local.whBulk_students
 * 4) تفتح واتساب ويب في تبويب جديد
 ***/
async function openWhatsAppBulk() {
  // 1) افتح WhatsApp Web فوراً في تبويب جديد
  const newTab = window.open("https://web.whatsapp.com/?whBulk=1", "_blank");
  if (!newTab) {
    return alert("⚠️ تعذّر فتح تبويب جديد. تأكد من عدم حظر النوافذ المنبثقة.");
  }

  // 2) نقرأ sid من URL
  const match = location.pathname.match(/Index\/([^\/?]+)/);
  const sid = match ? match[1] : null;
  if (!sid) {
    return alert("⚠️ تعذّر التعرف على معرّف المدرسة (sid).");
  }

  // 3) نجمع الطلاب في الخلفية (بدون انتظار)
  window.collectStudents(sid)
    .then(async () => {
      // 4) ننتظر حتى تظهر studentsList في chrome.storage.local (polling خفيف)
      let studentsList = [];
      for (let i = 0; i < 20; i++) { // محاولات لـ 5 ثوانٍ (20 × 250ms)
        studentsList = await new Promise(r =>
          chrome.storage.local.get("studentsList", res => r(res.studentsList || []))
        );
        if (Array.isArray(studentsList) && studentsList.length) break;
        await new Promise(r => setTimeout(r, 250));
      }
      if (!Array.isArray(studentsList) || !studentsList.length) {
       // console.warn("⚠️ لم يتم جلب بيانات الطلاب أو استغرق الأمر أكثر من اللازم.");
        return;
      }

      // 5) نبسط البيانات مباشرة ثم نخزّنها في whBulk_students
      const simple = studentsList.map(s => ({
        name:       s.name,
        phone:      s.parentPhone,
        classLabel: s.classLabel
      }));
      chrome.storage.local.set({ whBulk_students: simple }, () => {
      //  console.log("💾 [Madrasati→WhatsApp] خزّنا بيانات الطلاب في chrome.storage.local.whBulk_students");
      });
    })
    .catch(err => {
    //  console.error("❌ خطأ أثناء جمع الطلاب في الخلفية:", err);
    });
}

// ==== نهاية إضافة دوال WhatsApp إلى content.js ====


(async function(){
  /*** 0) دالة لإظهار رسالة في مودال صغير بدل alert ***/
  function showMessageModal(message) {
    const existing = document.getElementById("messageModal");
    if (existing) existing.remove();
    const ov = document.createElement("div");
    ov.id = "messageModal";
    ov.innerHTML = `
      <div style="
        position:fixed;top:0;left:0;right:0;bottom:0;
        background:rgba(0,0,0,0.5);
        display:flex;align-items:center;justify-content:center;
        z-index:10001;">
        <div style="
          background:white;padding:20px;
          width:90%;max-width:400px;
          border-radius:8px;
          position:relative;
          text-align:center;
        ">
          <button id="closeMsg" style="
            position:absolute;top:10px;left:10px;
            border:none;background:none;font-size:20px;
            cursor:pointer;">×</button>
          <p style="margin-top:20px;font-size:16px;">${message}</p>
        </div>
      </div>`;
    document.body.append(ov);
    ov.querySelector("#closeMsg").onclick = () => ov.remove();
  }

  /*** 1) نعمل شيء فقط إذا كنا في صفحة /Actions/Index/ ***/
  if (!location.pathname.startsWith("/SchoolManagment/Actions/Index/")) return;

  /*** 2) مساعدة بسيطة للتعامل مع localStorage بواسطة مفتاح موحّد ***/
  const ls = {
    get(k){ return localStorage.getItem(k); },
    set(k,v){ localStorage.setItem(k,v); },
    del(k){ localStorage.removeItem(k); },
    has(k){ return localStorage.getItem(k)!=null; }
  };

  /*** 3) إحضار وتخزين currentSid ***/
  let currentSid;
  (function manageSchoolId(){
    const m = location.pathname.match(/Index\/([^\/?]+)/);
    if (!m) return //console.warn("Madrasati+: لم أجد SchoolId");
    currentSid = m[1];
    ls.set("madrasati_last_school_id", currentSid);
  })();

  /*** 4) دالة مساعدة لإنشاء مفتاح موحّد لكل مدرسة ***/
  function sk(key){ return `${key}_${currentSid}`; }

  /*** 5) دوال مساعدات للترجمة وحشو القوالب ***/
  function trField(f){
    return {
      name:        "اسم الطالب",
      civilId:     "رقم الهوية",
      phone:       "رقم الجوال",
      parentPhone: "جوال ولي الأمر",
      username:    "اسم المستخدم"
    }[f]||f;
  }
  function applyTpl(tpl, vars){
    return tpl.replace(/{{(\w+)}}/g, (_,k)=>(vars[k]||""));
  }

  /*** 6) جلب مدير المدرسة واسم المدرسة ***/
  (function grabSchoolInfo(){
    document.querySelectorAll("div.mx-2").forEach(div=>{
      const txt = div.innerText.trim();
      if (txt.includes("مرحبا بك")){
        const span = div.querySelector("span")?.innerText.trim();
        if (span) ls.set(sk("manager_name"), span);
      }
      if (/ابتدائية|متوسطة|ثانوية|روض/.test(txt)){
        const name = txt.replace(/قائمة\s+المدارس?/, "").trim();
        ls.set(sk("school_name"), name);
      }
    });
  })();

  /*** 7) تحميل قوالب HTML من مجلد templates ***/
  const tplSection = await fetch(chrome.runtime.getURL("templates/section.html")).then(r=>r.text());
  const tplReport  = await fetch(chrome.runtime.getURL("templates/report.html")).then(r=>r.text());

  /*** 8) حفظ الكشوف في localStorage ***/
  function saveReport(name, config){
    const key = `savedReports_${currentSid}`;
    const list = JSON.parse(ls.get(key)||"[]");
    list.push({ name, config, date: Date.now() });
    ls.set(key, JSON.stringify(list));
    showMessageModal("✅ تم حفظ الكشف في كشوفي");
  }
  function showSavedReports(){
    const key = `savedReports_${currentSid}`;
    const reports = JSON.parse(ls.get(key)||"[]");
    const c = document.getElementById("tab-content");
    if (!reports.length){
      c.innerHTML = "<p>❗ لا توجد كشوف محفوظة</p>";
      return;
    }
    let html = "<ul style='list-style:none;padding:0;'>";
    reports.forEach((r,i)=>{
      html += `<li style="margin-bottom:8px;">
        <strong>${r.name}</strong>
        <button class="btn btn-sm btn-success" data-i="${i}" data-act="view">عرض</button>
        <button class="btn btn-sm btn-danger"  data-i="${i}" data-act="del">حذف</button>
      </li>`;
    });
    html += "</ul>";
    c.innerHTML = html;
    c.querySelectorAll("button[data-i]").forEach(btn=>{
      btn.onclick = ()=>{
        const i = +btn.dataset.i, rpt = reports[i];
        if (btn.dataset.act==="view") {
          const students = JSON.parse(ls.get(sk("students_list"))||"[]");
          if (rpt.config.type==="ready") {
            generateCustom(students, [rpt.config.class], ["name"], Array(rpt.config.extras).fill(""));
          } else {
            generateCustom(students, rpt.config.classes, rpt.config.fields, rpt.config.extras);
          }
        } else {
          reports.splice(i,1);
          ls.set(key, JSON.stringify(reports));
          showSavedReports();
        }
      };
    });
  }

  /*** 9) إنشـاء الكشف بناءً على التكوين المُختار ***/
  function generateCustom(data, classes, fields, extras){
    const vars = {
      EDU_ADMIN: ls.get(sk("education_administration"))||"",
      SCHOOL_NAME: ls.get(sk("school_name"))||"",
      YEAR: ls.get(sk("school_year"))||"",
      SEMESTER: ls.get(sk("school_semester"))||"",
      LOGO_URL: chrome.runtime.getURL("img/logo.svg")
    };
    let out = "";
    classes.forEach(cls=>{
      const hdr = fields.map(f=>`<th>${trField(f)}</th>`).join("")
                + extras.map((h,i)=>`<th>${h||i+1}</th>`).join("");
      const rows = data.filter(s=>s.classLabel===cls)
        .sort((a,b)=>a.name.localeCompare(b.name,"ar"))
        .map((s,i)=>`
          <tr>
            <td>${i+1} - ${s.name}</td>
            ${fields.filter(f=>"name"!==f).map(f=>`<td>${s[f]||""}</td>`).join("")}
            ${extras.map(_=>"<td></td>").join("")}
          </tr>`).join("");
      out += applyTpl(tplSection, {
        ...vars, CLASS:cls, MANAGER: ls.get(sk("manager_name"))||"",
        TABLE_HEADERS:hdr, ROWS:rows
      });
    });
    const w = window.open("","_blank");
    w.document.write(tplReport.replace("<!-- INSERT_SECTIONS_HERE -->", out));
    w.document.close();
  }
  function generateWeek(data, cls){
    const vars = {
      EDU_ADMIN: ls.get(sk("education_administration"))||"",
      SCHOOL_NAME: ls.get(sk("school_name"))||"",
      YEAR: ls.get(sk("school_year"))||"",
      SEMESTER: ls.get(sk("school_semester"))||"",
      LOGO_URL: chrome.runtime.getURL("img/logo.svg")
    };
    const hdr = `
      <th>اسم الطالب</th>
      <th>الأحد</th><th>الإثنين</th>
      <th>الثلاثاء</th><th>الأربعاء</th>
      <th>الخميس</th>`;
    const rows = data.sort((a,b)=>a.name.localeCompare(b.name,"ar"))
      .map((s,i)=>`
        <tr>
          <td>${i+1} - ${s.name}</td>
          <td></td><td></td><td></td><td></td><td></td>
        </tr>`).join("");
    const out = applyTpl(tplSection, {
      ...vars, CLASS:cls, MANAGER: ls.get(sk("manager_name"))||"",
      TABLE_HEADERS:hdr, ROWS:rows
    });
    const w = window.open("","_blank");
    w.document.write(tplReport.replace("<!-- INSERT_SECTIONS_HERE -->", out));
    w.document.close();
  }

  /*** 10) جلب وتخزين بيانات الطلاب عند الضغط على "🔄 تحديث بيانات الطلاب" ***/
  async function onRefresh(){
    const btn = this;
    btn.disabled = true; btn.innerText = "⏳ تحديث…";
    try {
      const base = `https://schools.madrasati.sa/SchoolManagment/Students/ManageStudents/${currentSid}`;
      let page=1, links=[], data=[];
      while(true){
        const doc = new DOMParser().parseFromString(
          await (await fetch(`${base}?PageNumber=${page}&years=-1&semesters=-1&classrooms=-1`)).text(),"text/html"
        );
        const hrefs = [...doc.querySelectorAll("a[href*='StudentInfo']")].map(a=>a.href);
        if (!hrefs.length) break;
        links.push(...hrefs); page++;
      }
      links = [...new Set(links)];
      for (let i=0; i<links.length; i++){
        btn.innerText = `🔄 ${i+1}/${links.length}`;
        const doc = new DOMParser().parseFromString(
          await (await fetch(links[i])).text(),"text/html"
        );
        const name  = doc.querySelector("h2#FullNameId")?.innerText.trim()||"";
        const civil = doc.querySelector("#NationalId")?.value.trim()||"";
        const phone = doc.querySelector("#Mobile")?.value.trim()||"";
        const pph   = doc.querySelector("#ParentMobile")?.value.trim()||"";
        const user  = doc.querySelector("label[for='MicrosoftUserName']")?.innerText.trim()||"";
        let raw=""; const h6=doc.querySelector("h6.fw-medium");
        if(h6){ const c=h6.cloneNode(true); c.querySelector("small")?.remove(); raw=c.innerText.trim(); }
        raw = raw.replace(/[٠-٩]/g,d=>String.fromCharCode(d.charCodeAt(0)-0x0660+0x30));
        const parts = raw.match(/^(.+?)[\s-]?(\d+)$/u);
        const cl = parts?`${parts[1].trim()}-${parts[2]}`:raw;
        data.push({ name, civilId:civil, phone, parentPhone:pph, username:user, classLabel:cl });
      }
      try {
        const crDoc = new DOMParser().parseFromString(
          await (await fetch(`https://schools.madrasati.sa/SchoolStructure/ClassRooms?SchoolId=${currentSid}`)).text(),"text/html"
        );
        const info = crDoc.querySelector("div.row.d-flex.mx-2 small")?.innerText;
        const m2 = info?.match(/العام الدراسي\s*(\d+).*الفصل الدراسي\s*([^\s]+)/);
        if (m2){
          ls.set(sk("school_year"), m2[1]);
          ls.set(sk("school_semester"), m2[2]);
        }
      } catch{}
      ls.set(sk("students_list"), JSON.stringify(data));
      ls.set(sk("last_refresh"), Date.now().toString());
      ls.set(sk("classrooms_list"), JSON.stringify([...new Set(data.map(s=>s.classLabel))]));
      showMessageModal("✅ تم التحديث");
    } catch(e){
      console.error(e); showMessageModal("❌ فشل التحديث");
    } finally {
      btn.disabled=false; btn.innerText="🔄 تحديث بيانات الطلاب";
    }
  }

  /*** 11) بناء تبويبات مودال “كشوف” ***/
  function buildTabs(){
    const c = document.getElementById("madrasati-content");
    c.innerHTML = `
      <div style="display:flex;gap:10px;margin-bottom:15px;">
        <button id="tab-custom" class="btn btn-primary">مصمم الكشوف</button>
        <button id="tab-ready"  class="btn btn-secondary">كشف جاهز</button>
        <button id="tab-saved"  class="btn btn-info">كشوفي</button>
      </div>
      <div id="tab-content"></div>`;
    c.querySelector("#tab-custom").onclick = showCustomForm;
    c.querySelector("#tab-ready").onclick  = showReadyForm;
    c.querySelector("#tab-saved").onclick  = showSavedReports;
    showCustomForm();
  }

  /*** 12) قالب “مصمم الكشوف” داخل المودال ***/
  function showCustomForm(){
    const data   = JSON.parse(ls.get(sk("students_list"))||"[]");
    const c      = document.getElementById("tab-content");
    const yr     = ls.get(sk("school_year"))||"";
    const sem    = ls.get(sk("school_semester"))||"";
    let classes = JSON.parse(ls.get(sk("classrooms_list"))||"[]");
    if (!classes.length) classes = [...new Set(data.map(s=>s.classLabel))];
    if (!data.length){
      c.innerHTML="<p>❗ لا توجد بيانات طلاب</p>"; return;
    }
    const clsHTML = classes.map(cl=>`
      <li><label>
        <input type="checkbox" class="class-checkbox" value="${cl}"> ${cl}
      </label></li>`).join("");
    const fields = ["name","civilId","phone","parentPhone","username"];
    const fieldsHTML = fields.map(f=>`
      <label style="display:inline-flex;align-items:center;gap:4px;margin-right:10px;">
        <input type="checkbox" class="field-checkbox" value="${f}" ${f==="name"?"checked":""}>
        ${trField(f)}
      </label>`).join("");
    c.innerHTML = `
      <div>
        <strong>العام الدراسي:</strong> ${yr} هـ  
        <strong>الفصل الدراسي:</strong> ${sem}
      </div>
      <h5>اختيار الفصول:</h5>
      <div style="position:relative;margin-bottom:15px;">
        <div class="select-btn" style="background:#f1f1f1;padding:8px;
             cursor:pointer;border-radius:4px;">اختر الفصول ⏷</div>
        <ul class="options" style="list-style:none;padding:5px;border:1px solid #ccc;
            position:absolute;top:100%;left:0;right:0;background:#fff;
            max-height:150px;overflow:auto;display:none;z-index:10001;">
          ${clsHTML}
        </ul>
      </div>
      <h5>اختيار الحقول:</h5>
      <div style="background:#f9f9f9;padding:10px;border-radius:4px;
                  margin-bottom:15px;">${fieldsHTML}</div>
      <h5>إضافة حقول مخصصة:</h5>
      <div id="customFields"></div>
      <button id="addField" class="btn btn-outline-secondary btn-sm" style="margin:5px 0;">➕ إضافة حقل</button>
      <div style="margin-top:20px;display:flex;align-items:center;gap:10px;">
        <button id="runReport" class="btn btn-success">📄 إنشاء الكشف</button>
        <label style="margin-left:10px;">
          <input type="checkbox" id="saveConfig">  حفظ في كشوفي
        </label>
      </div>`;
    c.querySelector(".select-btn").onclick = ()=>{
      const o = c.querySelector(".options");
      o.style.display = o.style.display==="block"?"none":"block";
    };
    document.getElementById("addField").onclick = ()=>{
      const d = document.createElement("div");
      d.innerHTML = `
        <div style="display:flex;align-items:center;margin:5px 0;">
          <input type="text" placeholder="اسم الحقل" class="form-control custom-input" style="flex:1">
          <span class="remove" style="cursor:pointer;color:red;margin-left:5px;">×</span>
        </div>`;
      c.querySelector("#customFields").append(d);
      d.querySelector(".remove").onclick = ()=>d.remove();
    };
    document.getElementById("runReport").onclick = ()=>{
      const sc = [...c.querySelectorAll(".class-checkbox:checked")].map(e=>e.value);
      const sf = [...c.querySelectorAll(".field-checkbox:checked")].map(e=>e.value);
      const ex = [...c.querySelectorAll(".custom-input")].map(i=>i.value.trim()).filter(v=>v);
      if (!sc.length||!sf.length) return showMessageModal("❗ اختر فصل وحقل على الأقل");
      if (document.getElementById("saveConfig").checked) {
        saveReport(
          `مصمم: ${sc.join(",")}`,
          { type:"custom", classes:sc, fields:sf, extras:ex }
        );
      }
      generateCustom(data, sc, sf, ex);
    };
  }

  /*** 13) قالب “كشف جاهز” داخل المودال ***/
  function showReadyForm(){
    const data  = JSON.parse(ls.get(sk("students_list"))||"[]");
    const c     = document.getElementById("tab-content");
    let classes= JSON.parse(ls.get(sk("classrooms_list"))||"[]");
    if (!classes.length) classes = [...new Set(data.map(s=>s.classLabel))];
    if (!data.length){
      c.innerHTML="<p>❗ لا توجد بيانات</p>"; return;
    }
    let html = "";
    classes.forEach(cl=>{
      html += `
        <div style="margin-bottom:10px;border-bottom:1px solid #ddd;padding:10px;">
          <div style="display:flex;justify-content:space-between;align-items:center;">
            <strong>الفصل:</strong> ${cl}
            <div style="display:flex;gap:5px;">
              <button class="btn btn-outline-primary btn-sm ready-btn" data-cl="${cl}" data-n="5">كشف 5</button>
              <button class="btn btn-outline-primary btn-sm ready-btn" data-cl="${cl}" data-n="10">كشف 10</button>
              <button class="btn btn-outline-primary btn-sm ready-btn" data-cl="${cl}" data-n="30">كشف 30</button>
              <button class="btn btn-outline-info btn-sm data-btn" data-cl="${cl}">بيانات الطلاب</button>
              <button class="btn btn-outline-info btn-sm week-btn" data-cl="${cl}">كشف الأسبوع</button>
            </div>
          </div>
        </div>`;
    });
    c.innerHTML = html;
    c.querySelectorAll(".ready-btn").forEach(btn=>
      btn.onclick = ()=> generateCustom(data,[btn.dataset.cl],["name"],Array(+btn.dataset.n).fill(""))
    );
    c.querySelectorAll(".data-btn").forEach(btn=>
      btn.onclick = ()=> generateCustom(data,[btn.dataset.cl],["name","civilId","phone","parentPhone","username"],[])
    );
    c.querySelectorAll(".week-btn").forEach(btn=>
      btn.onclick = ()=> generateWeek(data.filter(s=>s.classLabel===btn.dataset.cl),btn.dataset.cl)
    );
  }

  /*** 14) مودال “كشوف” ***/
  function openCustomModal(){
    if (document.getElementById("madrasati-modal")) return;
    const ov = document.createElement("div"); ov.id="madrasati-modal";
    ov.innerHTML = `
      <div style="position:fixed;top:0;left:0;right:0;bottom:0;
                  background:rgba(0,0,0,0.5);
                  display:flex;align-items:center;justify-content:center;
                  z-index:10000;">
        <div style="background:white;padding:20px;width:90%;max-width:600px;
                    max-height:80vh;overflow:auto;position:relative;border-radius:8px;">
          <button id="madrasati-close" style="position:absolute;top:10px;left:10px;
                                              border:none;background:none;font-size:20px;cursor:pointer;">×</button>
          <div id="madrasati-content"></div>
        </div>
      </div>`;
    document.body.append(ov);
    ov.querySelector("#madrasati-close").onclick = ()=>ov.remove();
    buildTabs();
  }

  /*** 15) مودال “التعليمات” و“الإعدادات” وحقن الزر الجانبي ***/
  function openInstructionsModal(){
    if (document.getElementById("instructionsModal")) return;
    const ov = document.createElement("div"); ov.id="instructionsModal";
    ov.innerHTML = `
      <div style="position:fixed;top:0;left:0;right:0;bottom:0;
                  background:rgba(0,0,0,0.5);
                  display:flex;align-items:center;justify-content:center;
                  z-index:10000;">
        <div style="background:white;padding:20px;width:90%;max-width:500px;
                    position:relative;border-radius:8px;">
          <button id="closeInstr" style="position:absolute;top:10px;left:10px;
                                         border:none;background:none;font-size:20px;cursor:pointer;">×</button>
          <h3>📖 تعليمات استخدام الإضافة</h3>
          <p>1. اضغط "🔄 تحديث بيانات الطلاب" لجلب أحدث البيانات.</p>
          <p>2. اضغط “📝 كشوف” ثم اختر تبويبة مصمم الكشوف لتصميم الكشف يمكنك حفظ الكشف وسوف تجده في تبويبة كشوفي، أو تبويبة كشف جاهز للاطلاع على الكشوف الجاهزة.</p>
          <p>3. اضغط "⚙️ إعدادات" لتعيين الإدارة العامة.</p>
          <br>
          <img
            src="${chrome.runtime.getURL('img/logo_kushoof.svg')}"
            alt="مصمم الكشوف"
            style="
              display: block;
              margin: 0 auto;
              width: 190px;
              height: 65px;
              object-fit: contain;
            "
          >
          <div style="text-align: center; margin-top: 30px; font-family: Arial, sans-serif;">
            <p style="font-size: 16px; color: #333; margin-bottom: 10px;">
              للدعم والاقتراحات تواصل معنا على منصة X أو البريد الالكتروني 
            </p>
            <div style="display: inline-flex; align-items: center; gap: 20px;">
              <a href="https://twitter.com/kushoofapp"
                 target="_blank"
                 title="تابعنا على X"
                 style="display: inline-block; text-decoration: none;">
                <img src="${chrome.runtime.getURL('img/X_logo_2023.svg')}"
                     alt="X Logo"
                     style="width: 30px; height: 30px; object-fit: contain; display: block;">
              </a>
              <a href="mailto:<EMAIL>"
                 target="_blank"
                 title="راسلنا عبر الإيميل"
                 style="display: inline-block; text-decoration: none;">
                <img src="${chrome.runtime.getURL('img/email.jpg')}"
                     alt="Email Icon"
                     style="width: 40px; height: 40px; object-fit: contain; display: block;">
              </a>
            </div>
          </div>
        </div>
      </div>`;
    document.body.append(ov);
    ov.querySelector("#closeInstr").onclick = ()=>ov.remove();
  }

  function openSettingsModal(){
    if (document.getElementById("settingsModal")) return;
    const ov = document.createElement("div"); ov.id="settingsModal";
    ov.innerHTML = `
      <div style="position:fixed;top:0;left:0;right:0;bottom:0;
                  background:rgba(0,0,0,0.5);
                  display:flex;align-items:center;justify-content:center;
                  z-index:10000;">
        <div style="background:white;padding:20px;width:90%;max-width:400px;
                    position:relative;border-radius:8px;">
          <button id="closeSet" style="position:absolute;top:10px;left:10px;
                  border:none;background:none;font-size:20px;cursor:pointer;">×</button>
          <h3>⚙️ إعدادات</h3>
          <label style="display:block;margin-bottom:20px;">
            الإدارة العامة:<br>
            <input id="inpEdu" type="text" class="form-control"
                   placeholder="الإدارة..." value="${ls.get(sk("education_administration"))||''}"
                   style="width:90%;height:40px;border:1px solid #ccc;border-radius:4px;padding:4px;">
          </label>
          <button id="saveSet" class="btn btn-primary">💾 حفظ</button>
        </div>
      </div>`;
    document.body.append(ov);
    ov.querySelector("#closeSet").onclick = ()=>ov.remove();
    ov.querySelector("#saveSet").onclick = ()=>{
      const edu = document.getElementById("inpEdu").value.trim();
      if (!edu){
        showMessageModal("❗ يرجى تعبئة حقل الإدارة العامة");
        return;
      }
      ls.set(sk("education_administration"), edu);
      showMessageModal("✅ تم حفظ الإعدادات");
      ov.remove();
    };
  }

  /*** 16) دالة حقن الأزرار مع إضافة زرّ “💬 واتساب” داخل wrap ***/
  function injectBtns() {
    // إذا فتحنا Madrasati من تبويب جديد بواسطة WhatsApp فلا نفعل شيئًا
    if (window.opener) return;
  
    // إذا كان wrap موجودًا مسبقًا، لا نكرر الحقن
    if (document.getElementById("madrasati-buttons")) return;
  
    // إنشاء العنصر wrap
    const wrap = document.createElement("div");
    wrap.id = "madrasati-buttons";
    wrap.style.cssText = `
      position: fixed;
      bottom: 70px;
      right: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      z-index: 10000;
    `;
  
    // **هنا نضيف الشعار قبل الأزرار**
    const logo = document.createElement("img");
    logo.src = chrome.runtime.getURL("img/Sheets-Designer-Logo2.png");
    logo.style.cssText = "width:190px;height:55px;margin-bottom:4px;";
    wrap.append(logo);
  
    // دالة مساعدة لإنشاء زر بسرعة
    function createBtn(text, cls, handler) {
      const b = document.createElement("button");
      b.className = cls;
      b.innerText = text;
      b.style.width = "190px";
      b.onclick = handler;
      return b;
    }
  
    // نضيف الأزرار إلى wrap
    wrap.append(
      createBtn("🔄 تحديث بيانات الطلاب", "btn btn-success", onRefresh),
      createBtn("📝 كشوف",            "btn btn-warning", openCustomModal),
      createBtn("💬 واتساب",          "btn btn-success", openWhatsAppBulk),
      createBtn("⚙️ إعدادات",         "btn btn-info",    openSettingsModal),
      createBtn("ℹ️ التعليمات",        "btn btn-secondary", openInstructionsModal)
    );
  
    // أخيرًا نُلصق wrap في body
    document.body.append(wrap);
  }
  

  // حقن الأزرار للمرة الأولى
  injectBtns();

  /*** 17) آلية تحديث تلقائي كل ثلاث أيام (كالسابق) ***/
  const threeDays = 3*24*60*60*1000;
  const last      = +ls.get(sk("last_refresh")||"0");
  if (!ls.has(sk("initialized"))){
    ls.set(sk("initialized"), "1");
    setTimeout(async()=>{
      const btn = document.querySelector("#madrasati-buttons .btn-success");
      if (btn){ await onRefresh.call(btn); openSettingsModal(); }
    },500);
  } else if (Date.now() - last > threeDays){
    setTimeout(async()=>{
      const btn = document.querySelector("#madrasati-buttons .btn-success");
      if (btn) await onRefresh.call(btn);
    },500);
  }

  /*** 18) تتبع pushState و popstate لإعادة حقن الأزرار في كل تغيير عنوان ***/
  const origPush = history.pushState;
  history.pushState = function(){
    origPush.apply(this, arguments);
    setTimeout(injectBtns, 500);
  };
  window.addEventListener("popstate", ()=> setTimeout(injectBtns, 500));

  //console.log("⚡ content.js loaded");
})();
