// == content_whatsapp.js ==

(() => {
 // console.log("🚀 [Bulk Sender] Script بدأ التحميل");

  /*** 0) دالة لفحص ما إذا كان الرابط يحوي معامل whBulk ***/
  function isBulkMode() {
    try {
      const url = new URL(location.href);
      return url.searchParams.has("whBulk");
    } catch {
      return false;
    }
  }

  /*** 1) حقن CSS لتعديل واجهة واتساب Web ***/
  function injectCSSAdjustments() {
    const css = `
      /* قلّص #app ليشغل (100% - 320px)، لتبقى مساحة 320px للـSidebar */
      #app {
        position: relative !important;
        width: calc(100% - 320px) !important;
        float: left !important;
        height: 100vh !important;
        box-sizing: border-box !important;
      }
      /* Sidebar 320px باليمين */
      #bulkSidebar {
        position: fixed;
        top: 0;
        right: 0;
        width: 320px;
        height: 100vh;
        background: #fff;
        border-left: 1px solid #ddd;
        box-shadow: -2px 0 6px rgba(0,0,0,0.1);
        box-sizing: border-box;
        overflow-y: auto;
        z-index: 9999;
        padding: 16px;
        font-family: Tajawal, sans-serif;
        direction: rtl;
      }
      /* عناوين القوائم */
      #bulkSidebar h4 {
        margin: 0 0 12px;
        color: #25D366;
        text-align: center;
      }
      /* قائمة الفصول */
      #classList div {
        padding: 6px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
      }
      #classList div:hover {
        background: #f5f5f5;
      }
      #classList .selected {
        background: #e0f7e9;
      }
      /* قائمة الطلاب */
      #studentList div {
        padding: 6px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
      }
      #studentList div:hover {
        background: #f5f5f5;
      }
      /* قائمة المختارون */
      #chosenList div {
        padding: 4px 6px;
        margin-bottom: 4px;
        background: #f0f0f0;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 14px;
      }
      #chosenList button.remove-student {
        background: none;
        border: none;
        color: #c00;
        cursor: pointer;
        font-size: 16px;
        line-height: 1;
      }
      /* حقل الرسالة وزر الإرسال */
      #bulkMsg {
        width: 100%;
        height: 80px;
        margin: 12px 0;
        box-sizing: border-box;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 8px;
        resize: vertical;
      }
      #startBulk {
        width: 100%;
        padding: 10px;
        background: #25D366;
        color: #fff;
        border: none;
        border-radius: 4px;
        font-size: 16px;
        cursor: pointer;
      }
      /* رسالة الإشعار عند البدء */
      #bulkSidebar .notify {
        margin-top: 12px;
        font-size: 13px;
        color: #666;
        line-height: 1.4;
      }
    `;
    const styleTag = document.createElement("style");
    styleTag.textContent = css;
    document.head.appendChild(styleTag);
   // console.log("💅 [Bulk Sender] تمّ حقن CSS لتعديل واجهة واتساب");
  }

  /*** 2) إنشاء Sidebar (320px) على اليمين ***/
  function createSidebar() {
    if (document.getElementById("bulkSidebar")) return;

    const sb = document.createElement("div");
    sb.id = "bulkSidebar";
    sb.innerHTML = `
      <h4>💬 رسائل الواتساب</h4>
      <div id="classList"   style="max-height:120px;  overflow:auto; border:1px solid #eee; box-sizing:border-box;"></div>
      <div id="studentList" style="max-height:120px;  overflow:auto; border:1px solid #eee; margin-top:12px; box-sizing:border-box;"></div>
      <div id="chosenList"  style="max-height:100px;  overflow:auto; border:1px solid #eee; margin-top:12px; box-sizing:border-box;"></div>
      <textarea id="bulkMsg" placeholder="اكتب نص الرسالة هنا..."></textarea>
      <button id="startBulk">🚀 بدء الإرسال</button>
      <div class="notify"> 
        * اختر فصلًا أولًا ثم حدّد الطلاب؛ ثم اكتب الرسالة واضغط “بدء الإرسال”.<br>
       
      </div>
    `;
    document.body.appendChild(sb);
   // console.log("✅ [Bulk Sender] تمّ إنشاء Sidebar");

    document.getElementById("startBulk").onclick = initBulk;
  }

  /*** 3) تحميل وتخزين الطلاب من chrome.storage.local.whBulk_students ***/
  let students   = [];
  let classesArr = [];

  function loadStoredStudents() {
    chrome.storage.local.get("whBulk_students", ({ whBulk_students }) => {
      if (Array.isArray(whBulk_students) && whBulk_students.length) {
        students = whBulk_students.slice();
      //  console.log("🚀 [Bulk Sender] جلبنا", students.length, "طالب من الخزين");
        buildClassList();
      } else {
       // console.warn("🔕 [Bulk Sender] لا توجد بيانات في chrome.storage.local.whBulk_students");
        document.getElementById("classList").innerHTML = `
          <div style="padding:8px;color:#888;text-align:center;">
            لا توجد بيانات؛ ارجع إلى Madrasati أولًا.
          </div>`;
      }
    });
  }

  /*** 4) بناء قائمة الفصول الأولى ***/
  function buildClassList() {
    const classListDiv = document.getElementById("classList");
    classListDiv.innerHTML = "";

    classesArr = Array.from(new Set(students.map(s => s.classLabel))).sort();

    classesArr.forEach(cls => {
      const div = document.createElement("div");
      div.innerText  = cls;
      div.dataset.cls = cls;
      div.onclick    = () => selectClass(cls, div);
      classListDiv.appendChild(div);
    });

    if (classesArr.length) {
      const firstDiv = classListDiv.querySelector("div[data-cls]");
      selectClass(classesArr[0], firstDiv);
    }
  }

  /*** 5) عند اختيار فصل، عرض أسماء طلابه في القسم الثاني ***/
  function selectClass(cls, divElem) {
    document.querySelectorAll("#classList div").forEach(d => d.classList.remove("selected"));
    divElem.classList.add("selected");

    const subset        = students.filter(s => s.classLabel === cls);
    const studentListDiv = document.getElementById("studentList");
    studentListDiv.innerHTML = "";

    subset.forEach(s => {
      const div = document.createElement("div");
      div.innerText   = s.name;
      div.dataset.phone = s.phone;
      div.dataset.cls   = cls;
      if (isChosen(s.phone)) {
        div.style.color  = "#aaa";
        div.style.cursor = "default";
      } else {
        div.onclick = () => addChosen(s);
      }
      studentListDiv.appendChild(div);
    });
  }

  /*** 6) إضافة طالب إلى قائمة المختارون ***/
  function addChosen(studentObj) {
    const chosenListDiv = document.getElementById("chosenList");
    if (isChosen(studentObj.phone)) return;

    const div = document.createElement("div");
    div.innerHTML = `
      <span>${studentObj.name}</span>
      <button class="remove-student" data-phone="${studentObj.phone}">&times;</button>
    `;
    div.dataset.phone = studentObj.phone;
    chosenListDiv.appendChild(div);

    div.querySelector("button.remove-student").onclick = () => {
      div.remove();
      const sDiv = document.querySelector(`#studentList div[data-phone="${studentObj.phone}"]`);
      if (sDiv) {
        sDiv.style.color  = "";
        sDiv.style.cursor = "pointer";
        sDiv.onclick = () => addChosen(studentObj);
      }
    };

    const sDiv = document.querySelector(`#studentList div[data-phone="${studentObj.phone}"]`);
    if (sDiv) {
      sDiv.style.color  = "#aaa";
      sDiv.style.cursor = "default";
      sDiv.onclick = null;
    }
  }

  /*** 7) فحص ما إذا كان الرقم موجودًا في قائمة المختارون ***/
  function isChosen(phone) {
    return Boolean(
      document.querySelector(`#chosenList div[data-phone="${phone}"]`)
    );
  }

  /*** 8) إدارة حالة الإرسال باستخدام sessionStorage ***/
  function saveState(queue, msg) {
    sessionStorage.bulkQueue     = JSON.stringify(queue);
    sessionStorage.bulkMsg       = msg;
    if (sessionStorage.bulkNotified !== "true" && sessionStorage.bulkNotified !== "false") {
      sessionStorage.bulkNotified = "false";
    }
   // console.log("💾 [Bulk Sender] saveState:", queue, msg, sessionStorage.bulkNotified);
  }
  function loadState() {
    try {
      const queue    = JSON.parse(sessionStorage.bulkQueue || "[]");
      const msg      = sessionStorage.bulkMsg || "";
      const notified = sessionStorage.bulkNotified === "true";
     // console.log("📂 [Bulk Sender] loadState:", { queue, msg, notified });
      return { queue, msg, notified };
    } catch (e) {
    //  console.error("❌ parse loadState:", e);
      return { queue: [], msg: "", notified: false };
    }
  }
  function setNotified() {
    sessionStorage.bulkNotified = "true";
   // console.log("🔔 [Bulk Sender] bulkNotified = true");
  }
  function clearState() {
    sessionStorage.removeItem("bulkQueue");
    sessionStorage.removeItem("bulkMsg");
    sessionStorage.removeItem("bulkNotified");
   // console.log("🗑️ [Bulk Sender] clearState");
  }

  /*** 9) initBulk(): جمع أرقام المختارون + نص الرسالة ثم بدء التنقل ***/
  function initBulk() {
  //  console.log("▶️ [Bulk Sender] initBulk");
    const msg        = document.getElementById("bulkMsg").value.trim();
    const chosenDivs = Array.from(document.querySelectorAll("#chosenList div"));
    if (!msg || !chosenDivs.length) {
      return alert("✏️ الرجاء كتابة نص الرسالة واختيار طالب واحد على الأقل.");
    }
    const queue = chosenDivs.map(div => div.dataset.phone);
    sessionStorage.bulkNotified = "false";
    saveState(queue, msg);
    navigateNext();
  }

  /*** 10) navigateNext(): الانتقال لأوّل رقم في القائمة ***/
  function navigateNext() {
    const { queue, msg } = loadState();
    if (!queue.length) {
      clearState();
      return alert("🎉 تمّ الانتهاء من الإرسال.");
    }
    const phone = queue[0];
    const waUrl = `https://web.whatsapp.com/send?phone=${phone}&text=${encodeURIComponent(msg)}`;
  //  console.log("🌐 [Bulk Sender] navigateNext →", waUrl);
    window.location.href = waUrl;
  }

  /*** 11) tryAutoSend(): لصق الرسالة والضغط على إرسال ثم المتابعة ***/
  async function tryAutoSend() {
    const { queue, msg, notified } = loadState();
    if (!queue.length) {
    //  console.log("🔕 [Bulk Sender] لا توجد قائمة انتظار");
      return;
    }
    if (!notified) {
      alert("⚠️ جارٍ إرسال الرسائل أوتوماتيكيًا… الرجاء عدم إغلاق الصفحة.");
      setNotified();
    }

   // console.log("⏳ [Bulk Sender] ننتظر ظهور حقل الكتابة");
    let attempt = 0;
    let inputBox = null;
    while (attempt++ < 20) {
      inputBox = document.querySelector('[contenteditable="true"][data-tab][spellcheck="true"]');
      if (inputBox && inputBox.closest("footer")) {
     //   console.log("✅ [Bulk Sender] وجدنا حقل الكتابة:", inputBox);
        break;
      }
      await new Promise(r => setTimeout(r, 300));
    }
    if (!inputBox) {
    // console.error("❌ [Bulk Sender] تعذّر العثور على حقل الكتابة داخل الدردشة");
      return;
    }

    function pasteText(el, txt) {
      const dt  = new DataTransfer();
      dt.setData("text", txt);
      const evt = new ClipboardEvent("paste", {
        clipboardData: dt,
        bubbles: true
      });
      el.dispatchEvent(evt);
    }
    inputBox.focus();
    pasteText(inputBox, msg);

    await new Promise(r => setTimeout(r, 800));

   // console.log("🔍 [Bulk Sender] نبحث عن زرّ “إرسال”");
    attempt = 0;
    let sendBtn = null;
    while (attempt++ < 20) {
      sendBtn =
        document.querySelector('button[aria-label="إرسال"]') ||
        document.querySelector('button[aria-label="Send"]') ||
        document.querySelector('span[data-icon="send"]')?.closest("button");
      if (sendBtn && !sendBtn.disabled) {
     //   console.log("✅ [Bulk Sender] وجدنا زرّ الإرسال:", sendBtn);
        break;
      }
      await new Promise(r => setTimeout(r, 300));
    }
    if (!sendBtn) {
    //  console.error("❌ [Bulk Sender] تعذّر العثور على زرّ “إرسال”");
      return;
    }

    sendBtn.click();
   // console.log("✅ [Bulk Sender] أُرسلت رسالة إلى:", queue[0]);

    // إزالة الرقم الحالي ومتابعة الرقم التالي بعد 1.5 ثانية
    const newQueue = queue.slice(1);
    saveState(newQueue, msg);
    setTimeout(navigateNext, 1500);
  }

  /*** 12) عند تحميل الصفحة ***/
  window.addEventListener("load", () => {
   // console.log("📄 [Bulk Sender] وثيقة جاهزة");
    setTimeout(() => {
      if (!isBulkMode()) {
    //    console.log("🔕 [Bulk Sender] رابط WhatsApp لم يحوِّل معامل whBulk → لا نُنشئ القائمة");
        return;
      }

      // إذا وُجد معامل whBulk في URL، نزيله كي لا يظهر طويلاً
      const cleanUrl = location.origin + location.pathname;
      history.replaceState(null, "", cleanUrl);

      // ثم نتحقّق من وجود بيانات الطلاب في التخزين:
      chrome.storage.local.get("whBulk_students", ({ whBulk_students }) => {
        if (Array.isArray(whBulk_students) && whBulk_students.length) {
       //   console.log("📄 [Bulk Sender] بيانات الطلاب موجودة، نُنشئ القائمة");
          injectCSSAdjustments();
          createSidebar();
          loadStoredStudents();
          tryAutoSend();
        } else {
      //    console.warn("🔕 [Bulk Sender] لا توجد بيانات؛ الرجاء العودة من Madrasati أولًا");
        }
      });
    }, 500);
  });

  /*** 13) استماع لتغيّر chrome.storage.local ***/
  chrome.storage.onChanged.addListener((changes, area) => {
    if (area === "local" && changes.whBulk_students) {
     // console.log("🔄 [Bulk Sender] تغيّر ذخيرة الطلاب، نُعيد بناء القائمة");
      if (Array.isArray(changes.whBulk_students.newValue) && changes.whBulk_students.newValue.length) {
        if (!document.getElementById("bulkSidebar")) {
          injectCSSAdjustments();
          createSidebar();
        }
        loadStoredStudents();
      }
    }
  });
})();
