<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>كشف طلاب</title>
  <style>
   
       :root {
  --report-width: 280mm;        
  --first-col-width: 222px;    
  --numeric-col-width: 60px;   
}

   
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 10px;
      background: #ccc;
      display: flex;
      justify-content: center;
      align-items: flex-start;
    }

    .report-container {
      background: #fff;
      width: var(--report-width);
      padding: 5mm;
      box-shadow: 0 0 5px rgba(0,0,0,0.2);
      box-sizing: border-box;
      margin: 0 auto;
    }

    .section {
      margin-bottom: 45px;
      padding-bottom: 10px;
      page-break-after: always;
    }
    .section-divider {
      border: none;
      border-top: 0px dashed #aaa;
      margin: 30px 0;
    }
    .header-table {
      width: 100%;
      border: none;
      margin-bottom: 3mm;
    }
   
    .header-table,
    .header-table td,
    .header-table th {
      border: none !important;
    }

    
    table {
      width: 100%;
      border-collapse: collapse;
      table-layout: auto; 
      margin-bottom: 5mm;
      font-size: 0.85rem;
      font-weight: 600;
    }
    th, td {
      border: 1px solid #000;
      padding: 3px 4px;
      line-height: 1.3;
      text-align: center;
      white-space: normal;
      word-break: break-word;
      box-sizing: border-box;
    }
    
    
    th:first-child {
  width: var(--first-col-width);
  text-align:center;
}
td:first-child {
  width: var(--first-col-width);
  
  text-align: right;
}
    thead th {
      background-color: #74787b4a;
    }
    tbody tr:nth-child(even) {
      background: #f2f2f2;
    }
    th.numeric-col,
td.numeric-col {
  width: var(--numeric-col-width);
}
   
    @page {
      margin: 3mm 3mm 2mm 3mm; 
    }
    @media print {
      html, body {
        margin: 0 !important;
        padding: 0 !important;
        background: #fff !important;
      }
      .report-container {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
      }
      .section {
        margin: 0 !important;
        padding: 0 !important;
        page-break-inside: avoid;
        page-break-after: always;
      }
     
      table, th, td {
        border: 1px solid #000 !important;
      }
      .section-divider {
        display: none !important;
      }
    }
  </style>
  
</head>
<body dir="rtl">
  <div class="report-container">

    <!-- INSERT_SECTIONS_HERE -->
  </div>
</body>
</html>
